import _ from 'lodash';
import async from 'async';
import util from 'util';
import fs from 'fs.extra'
import {amazonS3Addr, notifyServiceAddr, mediaServerAddr, service, locationServerAddr, environment, orderTypeGroup} from '../../config';
import CONSTANTS from '../../const';
import MESSAGE from '../../message';
import uuid from 'uuid/v4';
import rp from 'request-promise'
const ms = require('ms')
// import geoip from 'geoip-lite';

export default {
  getConfigMap(req, res) {
    const platform = _.get(req, 'body.platform', '');
    const regionName = _.get(req, 'body.regionName', 'unknown');

    if (!platform) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS
      })
    }

    Config.get(CONSTANTS.CONFIG_TYPE.CONFIG_MAP, regionName, (err, data) => {
      if(_.isError(err)) {
        logger.logError([err], req.originalUrl, req.body);
      }

      let providerMap = {}

      if(!err && data && data.config && data.config[platform]) {
        providerMap.link = data.config.link
        providerMap.provider = data.config[platform].provider
      }

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: providerMap
      })
    })
  },
    getConfigForReview(req, res) {
      const platform = _.get(req, 'body.platform', '');
      const nativeVersion = _.get(req,'body.nativeVersion', 0);
      const regionName = _.get(req, 'body.regionName', 'unknown');

      Config.get(CONSTANTS.CONFIG_TYPE.SHOW_HIDE_PAYMENT, regionName, (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }

        let hide = false;

        if(!err && data) {
          const nativeVersionHide = _.get(data, `config.${platform}.nativeVersion`, 0);

          if(nativeVersion === nativeVersionHide) {
            hide = true;
          }
        }

        res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: hide
        })
      })
    },
    getConfigForBackgroundLocation(req, res) {
      let userInf;

      const getAuthenInf = (next) => {
        Members
          .findById(req.user.id, "ship.isAuthen")
          .lean()
          .exec((err, result) => {
            userInf = result;

            next();
          })
      }

      const getConfigInf = (next) => {
        const platform = req.body.platform || '';

        const config = {
          locationProvider: platform === 'android' ? "ACTIVITY_PROVIDER" : "DISTANCE_FILTER_PROVIDER",
          desiredAccuracy: "HIGH_ACCURACY",
          stationaryRadius: 50,
          debug: false,
          distanceFilter: 50,
          notificationTitle: 'Đang sử dụng vị trí của bạn',
          notificationText: 'Việc sử dụng vị trí của bạn giúp chúng tôi có thể thông báo đơn hàng cho bạn khi bạn không sử dụng ứng dụng',
          notificationIconSmall:'ic_bg_launcher',
          notificationIconLarge:'ic_bg_launcher',
          notificationIconColor:'#00eaaf',
          notificationsEnabled:false,
          startForeground:false,
          stopOnTerminate: false,
          fastestInterval:10000,
          interval:20000,
          activityType: "AutomotiveNavigation",
          pauseLocationUpdates: false,
          saveBatteryOnBackground: false,
          syncThreshold: 25,
          maxLocations: 200,
          serverUrl: `${locationServerAddr}/api/v2.0/member/update-location`,
          serverSyncUrl: `${locationServerAddr}/api/v2.0/member/sync-update-location`,
          httpHeaders: {
          },
          postTemplate: {
            lat: "@latitude",
            lng: "@longitude",
            speed: "@speed",
            bearing: "@bearing",
            updatedAt: "@time"
          }
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: config
        })
      }

      async.waterfall([
        // getAuthenInf,
        getConfigInf
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      })
    },
    feedback(req, res) {
      const member = req.user.id;
      const message = _.get(req, 'body.message', '');
      const images = _.get(req, 'body.image', []);
      let region = req.body.regionName;
      const filePathArr = [];

      images.map(image => {
        filePathArr.push(image.replace(mediaServerAddr, ''));
      });

      const checkParams = (next) => {
        if(typeof message !== 'string' || message.trim().length === 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          });
        }

        next(null);
      }

      const getRegion = (next) => {
        if(region) {
          return next();
        }

        Members
          .findById(req.user.id, "region")
          .lean()
          .exec((err, result) => {
            if(err || !result) {
              return next(err || new Error(`Not found user inf`));
            }

            region = result.region || 'hn';

            next();
          })
      }

      const saveToDb = (next) => {
        const obj = {
          member,
          message,
          region
        }

        if(images.length) {
          obj.image = filePathArr;
        }

        Feedback.create(obj, (err, result) => {
          if(err) {
            return next(err);
          }

          next({
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGE.USER.FEEDBACK
          });
        })
      }

      async.waterfall([
        checkParams,
        getRegion,
        saveToDb
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getSimpleFeedback(req, res) {
      let samples = [
        {
        title: "Ứng dụng",
        image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-app.png"
        },
        {
          title: "Tài xế",
          image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-shipper2.png"
        },
        {
          title: "Đơn hàng",
          image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-order2.png"
        },
        {
          title: "Thanh toán",
          image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-payment.png"
        },
        {
          title: "Khác",
          image: "https://media.heyu.asia/uploads/new-image-service/2021-07-30-khac.png"
        }
      ]
      if(req.body.modeApp === 'shipper') {
        samples = [
          {
          title: "Ứng dụng",
          image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-app.png"
          },
          {
            title: "Đơn hàng",
            image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-order2.png"
          },
          {
            title: "Thanh toán",
            image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-payment.png"
          },
          {
            title: "Khác",
            image: "https://media.heyu.asia/uploads/new-image-service/2021-07-30-khac.png"
          }
        ]
      }

      if(req.body.modeApp === 'staff') {
        samples = [
          {
          title: "Ứng dụng",
          image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-app.png"
          },
          {
            title: "Giao dịch",
            image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-order2.png"
          },
          {
            title: "Thanh toán",
            image: "https://media.heyu.asia/uploads/new-image-service/2021-07-28-payment.png"
          },
          {
            title: "Khác",
            image: "https://media.heyu.asia/uploads/new-image-service/2021-07-30-khac.png"
          }
        ]
      }

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: samples
      })
    },
    getFeedbackSample(req, res) {
      const bike = _.get(req, 'body.bike', 0);
      const hireDriver = _.get(req, 'body.hireDriver', 0);
      const care = _.get(req, 'body.care', 0);
      const clean = _.get(req, 'body.clean', 0);

      let samples = {
        "Phản hồi tài xế" : [
          {
            code:'DELAY',
            message:'Tài xế giao hàng chậm'
          },
          {
            code:'NOT_ARRIVE',
            message:'Tài xế không đến lấy hàng'
          },
          {
            code:'CHEAT_MONEY',
            message:'Tài xế thu thêm tiền'
          },
          {
            code:'NOT_FOUND',
            message:'Tôi không tìm được tài xế cho đơn hàng'
          },
          {
            code:'CANNOT_CONTACT',
            message:'Tôi không liên lạc được với tài xế'
          },
          {
            code:'ATTITUDE',
            message:'Tôi không hài lòng với thái độ phục vụ của tài xế'
          },
          {
            code:'WRONG_UPDATE',
            message:'Tài xế cập nhật sai trạng thái đơn hàng'
          }
        ],
        "Hỗ trợ huỷ đơn" : [
          {
            code:'REJECT_SHOP',
            message:'Tôi không huỷ được đơn hàng'
          }
        ],
        "Sai thông tin đơn hàng" : [
          {
            code:'WRONG_MONEY',
            message:'Đơn hàng sai giá'
          }
        ],
        "Đơn hàng gặp sự cố" : [
          {
            code:'DENY',
            message:'Khách không nhận đơn hàng'
          },
          {
            code:'BREAK',
            message:'Hàng của tôi bị hỏng trong quá trình vận chuyển'
          }
        ],
        "Thanh toán" : [
          {
            code:'PAYMENT',
            message:'Tôi gặp vấn đề khi thanh toán đơn hàng'
          }
        ]

      }

      if (bike) {
        samples = {
          "Phản hồi tài xế" : [
            {
              code:'NOT_ARRIVE_BIKE',
              message:'Tài xế không đến đón tôi'
            },
            {
              code:'CHEAT_MONEY',
              message:'Tài xế thu thêm tiền'
            },
            {
              code:'NOT_FOUND_BIKE',
              message:'Tôi không tìm được tài xế'
            },
            {
              code:'CANNOT_CONTACT',
              message:'Tôi không liên lạc được với tài xế'
            },
            {
              code:'ATTITUDE',
              message:'Tôi không hài lòng với thái độ phục vụ của tài xế'
            }
          ],
          "Hỗ trợ huỷ chuyến" : [
            {
              code:'REJECT_SHOP_BIKE',
              message:'Tôi không huỷ được chuyến đi'
            }
          ],
          "Sai thông tin chuyến đi" : [
            {
              code:'WRONG_MONEY_BIKE',
              message:'Chuyến đi sai giá'
            }
          ],
          "Thanh toán" : [
            {
              code:'PAYMENT_BIKE',
              message:'Tôi gặp vấn đề khi thanh toán chuyến đi'
            }
          ]
        }
      }

      if (hireDriver) {
        samples = {
          "Phản hồi tài xế" : [
            {
              code:'NOT_ARRIVE_BIKE',
              message:'Tài xế không đến đón tôi'
            },
            {
              code:'CHEAT_MONEY',
              message:'Tài xế thu thêm tiền'
            },
            {
              code:'NOT_FOUND_BIKE',
              message:'Tôi không tìm được tài xế'
            },
            {
              code:'CAR_PROBLEM',
              message:'Tài xế làm hỏng xe của tôi'
            },
            {
              code:'CANNOT_CONTACT',
              message:'Tôi không liên lạc được với tài xế'
            },
            {
              code:'ATTITUDE',
              message:'Tôi không hài lòng với thái độ phục vụ của tài xế'
            }
          ],
          "Hỗ trợ huỷ chuyến" : [
            {
              code:'REJECT_SHOP_BIKE',
              message:'Tôi không huỷ được chuyến đi'
            }
          ],
          "Sai thông tin chuyến đi" : [
            {
              code:'WRONG_MONEY_BIKE',
              message:'Chuyến đi sai giá'
            }
          ],
          "Thanh toán" : [
            {
              code:'PAYMENT_BIKE',
              message:'Tôi gặp vấn đề khi thanh toán chuyến đi'
            }
          ]
        }
      }

      if (care) {
        samples = {
          "Phản hồi chăm sóc viên": [
            {
              code: 'NOT_ARRIVE_CARE',
              message: 'Chăm sóc viên không đến phục vụ'
            },
            {
              code: 'CHEAT_MONEY',
              message: 'Chăm sóc viên thu thêm tiền'
            },
            {
              code: 'NOT_FOUND_CARE',
              message: 'Tôi không tìm được chăm sóc viên'
            },
            // {
            //   code: 'CAR_PROBLEM',
            //   message: 'Tài xế làm hỏng xe của tôi'
            // },
            {
              code: 'CANNOT_CONTACT',
              message: 'Tôi không liên lạc được với chăm sóc viên'
            },
            {
              code: 'ATTITUDE',
              message: 'Tôi không hài lòng với thái độ phục vụ của chăm sóc viên'
            }
          ],
          "Hỗ trợ huỷ giao dịch": [
            {
              code: 'REJECT_CUSTOMER_CARE',
              message: 'Tôi không huỷ được giao dịch'
            }
          ],
          "Sai thông tin giao dịch": [
            {
              code: 'WRONG_MONEY_CARE',
              message: 'Giao dịch sai giá'
            }
          ],
          "Thanh toán": [
            {
              code: 'PAYMENT_CARE',
              message: 'Tôi gặp vấn đề khi thanh toán giao dịch'
            }
          ]
        }
      }

      if (clean) {
        samples = {
          "Phản hồi nhân viên": [
            {
              code: 'NOT_ARRIVE_CLEAN',
              message: 'Nhân viên không đến làm việc'
            },
            {
              code: 'CHEAT_MONEY_CLEAN',
              message: 'Nhân viên thu thêm tiền'
            },
            {
              code: 'NOT_FOUND_CLEAN',
              message: 'Tôi không tìm được nhân viên'
            },
            {
              code: 'CANNOT_CONTACT_CLEAN',
              message: 'Tôi không liên lạc được với nhân viên'
            },
            {
              code: 'ATTITUDE_CLEAN',
              message: 'Tôi không hài lòng với thái độ phục vụ của nhân viên'
            }
          ],
          "Hỗ trợ huỷ đơn": [
            {
              code: 'REJECT_CUSTOMER_CLEAN',
              message: 'Tôi không huỷ được đơn'
            }
          ],
          "Sai thông tin đơn": [
            {
              code: 'WRONG_MONEY_CLEAN',
              message: 'Đơn sai giá'
            }
          ],
          "Thanh toán": [
            {
              code: 'PAYMENT_CLEAN',
              message: 'Tôi gặp vấn đề khi thanh toán đơn'
            }
          ]
        }
      }

      if(req.body.modeApp === 'shipper') {
        samples = {
          "Sai thông tin shop" : [
            {
              code:'CANNOT_CONTACT',
              message:'Tôi không liên lạc được với shop'
            },
            {
              code:'CANNOT_FIND_SHOP',
              message:'Tôi không tìm thấy địa chỉ shop'
            }
          ],
          "Hỗ trợ huỷ đơn" : [
            {
              code:'REJECT_SHIPPER',
              message:'Admin huỷ giúp tôi đơn hàng'
            }
          ],
          "Hỗ trợ chờ giao" : [
            {
              code:'PENDING',
              message:'Hỗ trợ chuyển trạng thái chờ giao'
            }
          ],
          "Sai thông tin đơn hàng" : [
            {
              code:'NOT_FOUND_RECEIVER',
              message:'Tôi không có thông tin nguời nhận'
            }
          ],
          "Đơn hàng gặp sự cố" : [
            {
              code:'DENY',
              message:'Khách không nhận đơn hàng'
            },
            {
              code:'BREAK_SHIP',
              message:'Hàng bị hỏng trong quá trình vận chuyển'
            }
          ]
        }

        if (bike) {
          samples = {
            "Sai thông tin khách hàng" : [
              {
                code:'CANNOT_CONTACT_BIKE',
                message:'Tôi không liên lạc được với khách hàng'
              },
              {
                code:'CANNOT_FIND_SHOP_BIKE',
                message:'Tôi không tìm thấy địa chỉ khách hàng'
              }
            ],
            "Hỗ trợ huỷ chuyến" : [
              {
                code:'REJECT_SHIPPER_BIKE',
                message:'Admin huỷ giúp tôi chuyến đi'
              }
            ]
          }
        }

        if (hireDriver) {
          samples = {
            "Sai thông tin khách hàng" : [
              {
                code:'CANNOT_CONTACT_BIKE',
                message:'Tôi không liên lạc được với khách hàng'
              },
              {
                code:'CANNOT_FIND_SHOP_BIKE',
                message:'Tôi không tìm thấy địa chỉ khách hàng'
              }
            ],
            "Hỗ trợ huỷ chuyến" : [
              {
                code:'REJECT_SHIPPER_BIKE',
                message:'Admin huỷ giúp tôi chuyến đi'
              }
            ]
          }
        }
      }

      if (req.body.modeApp === 'staff') {
        samples = {
          "Sai thông tin khách hàng": [
            {
              code: 'CANNOT_CONTACT_CARE',
              message: 'Tôi không liên lạc được với khách hàng'
            },
            {
              code: 'CANNOT_FIND_CUSTOMER_CARE',
              message: 'Tôi không tìm thấy địa chỉ khách hàng'
            }
          ],
          "Hỗ trợ huỷ ca làm việc": [
            {
              code: 'REJECT_STAFF_CARE',
              message: 'Admin huỷ giúp tôi ca làm việc'
            }
          ]
        }
      }

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: samples
      })
    },
    getFeedbackSampleGeneral(req, res) {
      let samples = {
        "Phản hồi về ứng dụng": [
          {
            code:'WRONG_LOCATION',
            message:'Ứng dụng không cập nhật đúng vị trí'
          },
          {
            code:'NOT_FOUND_SHIPPER',
            message:'Đặt lâu không có tài xế nhận'
          },
          {
            code:'PROMOTE',
            message:'Không nhập được mã khuyến mãi'
          },
          {
            code:'ERROR',
            message:'Ứng dụng bị lỗi'
          }
        ],
        "Phản hồi tài xế" : [
          {
            code:'UNIFORM',
            message:'Tài xế không mặc đồng phục khi nhận đơn'
          },
          {
            code:'DELAY',
            message:'Tài xế giao hàng chậm chễ'
          },
          {
            code:'NOT_ARRIVE',
            message:'Tài xế không đến lấy hàng'
          },
          {
            code:'CHEAT_MONEY',
            message:'Tài xế thu thêm tiền'
          },
          {
            code:'SHIPPER_REJECT',
            message:'Tài xế báo huỷ đơn hàng'
          },
          {
            code:'NOT_FOUND',
            message:'Tôi không tìm được tài xế cho đơn hàng'
          },
          {
            code:'CANNOT_CONTACT',
            message:'Tôi không liên lạc được với tài xế'
          },
          {
            code:'ATTITUDE',
            message:'Tài xế có thái độ không chuyên nghiệp'
          },
          {
            code:'SHIPPER_REJECT_PROMOTE',
            message:'Tài xế từ chối nhận đơn hàng khuyến mãi'
          },
          {
            code:'WRONG_UPDATE',
            message:'Tài xế cập nhật sai trạng thái đơn hàng'
          }
        ],
        "Phản hồi về đơn hàng" : [
          {
            code:'REJECT_SHOP',
            message:'Huỷ đơn, tìm tài xế mới'
          }
        ],
        "Thanh toán" : [
          {
            code:'PAYMENT',
            message:'Tôi gặp vấn đề khi thanh toán đơn hàng'
          }
        ]

      }



      if(req.body.modeApp === 'shipper') {
        samples = {
          "Phản hồi về đơn hàng" : [
            {
              code:'REJECT_SHIPPER',
              message:'Admin huỷ giúp tôi đơn hàng'
            },
            {
              code:'PENDING',
              message:'Hỗ trợ chuyển trạng thái chờ giao'
            },
            {
              code:'PHOT',
              message:'Báo phốt'
            },
            {
              code:'CANNOT_CONTACT',
              message:'Không liên lạc được shop/ khách'
            },
            {
              code:'DENY',
              message:'Khách không nhận đơn hàng'
            },
            {
              code:'BREAK_SHIP',
              message:'Hàng bị hỏng trong quá trình vận chuyển'
            }
          ],
          "Phản hồi về ứng dụng": [
            {
              code:'ERROR',
              message:'Ứng dụng bị lỗi'
            }
          ],
          "Phản hồi về tài khoản": [
            {
              code:'BLOCK',
              message:'Hỗ trợ mở tài khoản'
            },
            {
              code:'CHARGE',
              message:'Tôi gặp vấn đề nạp Coints'
            },
            {
              code:'SSM',
              message:'Chưa được cộng tiền SSM'
            }
          ]
        }
      }

      if (req.body.modeApp === 'staff') {
        samples = {
          "Phản hồi về ca làm việc": [
            {
              code: 'REJECT_STAFF',
              message: 'Admin huỷ giúp tôi ca làm việc'
            },
            // {
            //   code: 'PENDING',
            //   message: 'Hỗ trợ chuyển trạng thái chờ giao'
            // },
            {
              code: 'PHOT',
              message: 'Báo phốt'
            },
            {
              code: 'CANNOT_CONTACT',
              message: 'Không liên lạc được khách hàng'
            },
            {
              code: 'DENY',
              message: 'Khách không nhận ca làm việc'
            },
            // {
            //   code: 'BREAK_SHIP',
            //   message: 'Hàng bị hỏng trong quá trình vận chuyển'
            // }
          ],
          "Phản hồi về ứng dụng": [
            {
              code: 'ERROR',
              message: 'Ứng dụng bị lỗi'
            }
          ],
          "Phản hồi về tài khoản": [
            {
              code: 'BLOCK',
              message: 'Hỗ trợ mở tài khoản'
            },
            {
              code: 'CHARGE',
              message: 'Tôi gặp vấn đề nạp Coints'
            },
            {
              code: 'SSM',
              message: 'Chưa được cộng tiền SSM'
            }
          ]
        }
      }

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: samples
      })
    },
    sendFeedback(req, res) {
      const member = req.user.id;
      const mode = _.get(req, 'body.modeApp', '');
      const message = _.get(req, 'body.message', '');
      const images = _.get(req, 'body.image', []);
      const orders = _.get(req, 'body.orders', '')
      const orderStore = _.get(req, 'body.orderStore', []);
      const note = _.get(req, 'body.note', '')
      const bike = _.get(req, 'body.bike', 0);
      const care = _.get(req, 'body.care', 0);
      const clean = _.get(req, 'body.clean', 0);
      const jobId = _.get(req, 'body.jobId', '');
      let region = req.body.regionName;
      const filePathArr = [];

      images.map(image => {
        filePathArr.push(image.replace(mediaServerAddr, ''));
      });

      const checkParams = (next) => {
        if(typeof message !== 'string' || message.trim().length === 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGE.SYSTEM.ERROR
          });
        }

        next(null);
      }

      const saveToDb = (next) => {
        const obj = {
          member,
          message,
          region,
          mode,
          type:0
        }
        if(orders) {
          obj.orders = orders,
          obj.type = 1

          if (bike) {
            obj.type = 2;
          }

          if (care) {
            obj.type = 4;
          }

          if (clean) {
            obj.type = 5;
          }
        }

        if (jobId) {
          obj.jobId = jobId;
        }

        if(orderStore && orderStore.length) {
          obj.orderStore = orderStore,
          obj.type = 3
        }

        if(note && note.trim() !== '') {
          obj.note = note
        }

        if(images.length) {
          obj.image = filePathArr;
        }

        Feedback.create(obj, (err, result) => {
          if(err) {
            return next(err);
          }

          next({
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGE.USER.FEEDBACK
          });
        })
      }

      async.waterfall([
        checkParams,
        saveToDb
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGE.SYSTEM.ERROR
        });

        res.json(data || err);
      });
    },
    getFeedbackList(req, res) {
      const member = req.user.id;
      const mode = req.body.modeApp;
      const from = _.toSafeInteger(req.body.from) || Date.now();
      const limit = _.clamp(_.toSafeInteger(req.body.limit), 5, 100);
      const skip = _.get(req, 'body.skip', 0)
      let query = _.get(req, 'body.query', {});
      query.member = member,
      query.mode = mode
      query.createdAt = {'$lt': from }

      // Create a Map for O(1) lookup instead of O(n) nested loop
      const samplesMap = new Map([
        ['DELAY', 'Tài xế giao hàng chậm'],
        ['NOT_ARRIVE', 'Tài xế không đến lấy hàng'],
        ['CHEAT_MONEY', 'Tài xế thu thêm tiền'],
        ['NOT_FOUND', 'Tôi không tìm được tài xế cho đơn hàng'],
        ['CANNOT_CONTACT', 'Tôi không liên lạc được với tài xế'],
        ['ATTITUDE', 'Tôi không hài lòng với thái độ phục vụ của tài xế'],
        ['WRONG_UPDATE', 'Tài xế cập nhật sai trạng thái đơn hàng'],
        ['REJECT_SHOP', 'Tôi không huỷ được đơn hàng'],
        ['WRONG_MONEY', 'Đơn hàng sai giá'],
        ['DENY', 'Khách không nhận đơn hàng'],
        ['BREAK', 'Hàng của tôi bị hỏng trong quá trình vận chuyển'],
        ['PAYMENT', 'Tôi gặp vấn đề khi thanh toán đơn hàng'],
        ['NOT_ARRIVE_BIKE', 'Tài xế không đến đón tôi'],
        ['NOT_FOUND_BIKE', 'Tôi không tìm được tài xế'],
        ['ATTITUDE_BIKE', 'Tôi không hài lòng với thái độ phục vụ của tài xế'],
        ['REJECT_SHOP_BIKE', 'Tôi không huỷ được chuyến đi'],
        ['WRONG_MONEY_BIKE', 'Chuyến đi sai giá'],
        ['PAYMENT_BIKE', 'Tôi gặp vấn đề khi thanh toán chuyến đi'],
        ['NOT_ARRIVE_CARE', 'Chăm sóc viên không đến phục vụ'],
        ['NOT_FOUND_CARE', 'Tôi không tìm được chăm sóc viên'],
        ['ATTITUDE_CARE', 'Tôi không hài lòng với thái độ phục vụ của chăm sóc viên'],
        ['REJECT_CUSTOMER_CARE', 'Tôi không huỷ được giao dịch'],
        ['WRONG_MONEY_CARE', 'Giao dịch sai giá'],
        ['PAYMENT_CARE', 'Tôi gặp vấn đề khi thanh toán giao dịch'],
        ['NOT_ARRIVE_CLEAN', 'Nhân viên không đến làm việc'],
        ['CHEAT_MONEY_CLEAN', 'Nhân viên thu thêm tiền'],
        ['NOT_FOUND_CLEAN', 'Tôi không tìm được nhân viên'],
        ['CANNOT_CONTACT_CLEAN', 'Tôi không liên lạc được với nhân viên'],
        ['ATTITUDE_CLEAN', 'Tôi không hài lòng với thái độ phục vụ của nhân viên'],
        ['REJECT_CUSTOMER_CLEAN', 'Tôi không huỷ được đơn'],
        ['WRONG_MONEY_CLEAN', 'Đơn sai giá'],
        ['PAYMENT_CLEAN', 'Tôi gặp vấn đề khi thanh toán đơn']
      ]);

      const checkParams = (next) => {
        if(!member) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getList = (next) => {
        let func = Feedback
          .find(query)
          .lean()
          .limit(limit)
          .sort("-createdAt")
          .skip(skip);

        // Add caching for production environment
        if (environment === 'production') {
          const cacheKey = `feedback:${member}:${mode}:${from}:${limit}:${skip}`;
          func = func.cache(ms('5m')/1000, cacheKey);
        }

        func.exec((err, result) => {
          if (err) {
            return next(err);
          }

          // Optimize data processing with direct mapping
          const data = result.map((item) => {
            if (item.message.includes('Phản hồi về:')) {
              const message = item.message.replace("Phản hồi về:  ","")
              const arr = message.split(", ")
              return {
                ...item,
                typeName: arr,
                title: ''
              }
            } else {
              // O(1) lookup instead of O(n) loop
              const sampleMessage = samplesMap.get(item.message);
              if (sampleMessage) {
                return {
                  ...item,
                  title: sampleMessage,
                  message: sampleMessage,
                  typeName: []
                }
              }
              return {
                ...item,
                typeName: []
              }
            }
          });

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: data
          });
        })
      }
      async.waterfall([
        checkParams,
        getList
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getNewsCategory(req, res) {
      const now = Date.now();
      const region = _.get(req, 'body.regionName', '');
      const regionDistrict = _.get(req, 'user.regionDistrict', '');
      const from = _.get(req, 'body.from', now);
      const key = from > 100000000 ? `new_category:${region}` : `new_category:${region}:${from}`;

      const checkParams = (next) => {
        if(!region) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getNewsCategory = (next) => {
        let func = NewCategory.find({
          active: 1,
          order: {
            '$lt': from
          },
          $or: [
            {
              'region.allow': region
            },
            {
              'region.allow': regionDistrict
            },
            {
              'region.allow': 'all',
              'region.deny': {
                $ne: region
              }
            }
          ]
        }, "-createdAt -active -region")
        .sort("-order")
        .lean()

        if (environment === 'production') {
          func = func.cache(ms('15m')/1000, key)
        }

        func.exec((err, result) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          });
        })
      }

      async.waterfall([
        checkParams,
        getNewsCategory
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getNews(req, res) {
      const now = Date.now();
      const region = _.get(req, 'body.regionName', '');
      const regionDistrict = _.get(req, 'user.regionDistrict', '');
      const from = _.get(req, 'body.from', now);
      const category = _.get(req, 'body.category', 0);
      const limit = _.get(req, 'body.limit', 20);
      let key = '';
      let forNewShop = 0;

      const checkParams = (next) => {
        if(!region || !category) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const determineNewShop = (next) => {
        Members
          .findOne({
            _id: req.user.id
          })
          .lean()
          .exec((err,result) => {
            if(err) {
              return next(err)
            }
            if(result && result.shop && result.shop.totalPostOS > 2) {
              return next();
            }
            forNewShop = 1;
            next();
          })
      }

      const getNews = (next) => {
        key = from > 100000000 ? `news:${region}:${category}:forNewShop-${forNewShop}` : `news:${region}:${category}:forNewShop-${forNewShop}:from-${from}`
        let queryObj = {
          category,
          active: 1,
          order: {
            '$lt': from
          },
          $or: [
            {
              'region.allow': region
            },
            {
              'region.allow': regionDistrict
            },
            {
              'region.allow': 'all',
              'region.deny': {
                $ne: region
              }
            }
          ]
        }
        if(forNewShop) {
          queryObj = {
            category,
            $and:[
              {
                $or:[
                {
                  active: 1,
                },
                {
                  forNewShop: 1,
                }]
              },
              {
                $or: [
                  {
                    'region.allow': region
                  },
                  {
                    'region.allow': regionDistrict
                  },
                  {
                    'region.allow': 'all',
                    'region.deny': {
                      $ne: region
                    }
                  }
                ]
              }
            ],
            order: {
              '$lt': from
            }
          }
        }
        let func = New.find(queryObj, "-createdAt -active -region -data")
        .sort("-order")
        .limit(limit)
        .lean()

        if (environment === 'production') {
          func = func.cache(ms('15m')/1000, key)
        }

        func.exec((err, result) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          });
        })
      }

      async.waterfall([
        checkParams,
        determineNewShop,
        getNews
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getPromotionCategory(req, res) {
      const now = Date.now();
      const region = _.get(req, 'body.regionName', '');
      const from = _.get(req, 'body.from', now);
      // const key = from > 100000000 ? `new_category:${region}` : `new_category:${region}:${from}`;

      const checkParams = (next) => {
        if(!region) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getPromotionCategory = (next) => {
        let func = PromotionCategory.find({
          active: 1,
          order: {
            '$lt': from
          },
          $or: [
            {
              'region.allow': region
            },
            {
              'region.allow': 'all',
              'region.deny': {
                $ne: region
              }
            }
          ]
        }, "-createdAt -active -region")
        .sort("-order")
        .lean()

        // if (environment === 'production') {
        //   func = func.cache(ms('15m')/1000, key)
        // }

        func.exec((err, result) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          });
        })
      }

      async.waterfall([
        checkParams,
        getPromotionCategory
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getPromotionHeyU(req, res) {
      const now = Date.now();
      const region = _.get(req, 'body.regionName', '');
      const regionDistrict = _.get(req, 'user.regionDistrict', '');
      const from = _.get(req, 'body.from', now);
      const category = _.get(req, 'body.category', 0);
      const limit = _.get(req, 'body.limit', 3);
      let key = '';

      const checkParams = (next) => {
        if(!region || !category) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }

        next();
      }

      const getPromotionHeyU = (next) => {
        let queryObj = {
          category,
          active: 1,
          order: {
            '$lt': from
          },
          $or: [
            {
              'region.allow': region
            },
            {
              'region.allow': regionDistrict
            },
            {
              'region.allow': 'all',
              'region.deny': {
                $ne: region
              }
            }
          ]
        }
        let func = PromotionHeyU.find(queryObj, "-createdAt -active -region -data")
        .sort("-order")
        .limit(limit)
        .lean()

        if (environment === 'production') {
          func = func.cache(ms('15m')/1000, key)
        }

        func.exec((err, result) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          });
        })
      }

      async.waterfall([
        checkParams,
        getPromotionHeyU
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
      listServiceAvailableV2(req, res) {
        const region = req.body.regionName;
        const mode = req.body.modeApp;
        const userId = req.user.id;
        const platform = req.body.platform;
        const nativeVersion = req.body.nativeVersion;
        const newDelivery = req.body.newDelivery;
        const appName = req.body.appName;
        let blackArray = [];

        let isOrderActive = true
        let memberInf
        let showHub

        const checkParams = (next) => {
          if (!region || !userId || !platform || !nativeVersion) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS
            });
          }
          next();
        }

        const countBikeOrder = (next) => {
          OrderBike
            .count({
              shop: userId,
              updatedAt: {
                $gte: Date.now() - ms('1h')
              },
              status:{$in:[-1,0,1,2]}
            },(err, count) => {
              if(err) {
                return next(err)
              }
              if(count > 0) {
                blackArray = service.bike.concat(service.car)
              }
              next();
            })
        }

        const findUser = (next) => {
          if(region !== 'hn' && region !== 'vietnam:danang') {
            return next();
          }
          Members
            .findOne({
              _id: userId
            })
            .lean()
            .exec((err,result) => {
              if(err) {
                return next(err)
              }
              if(!result) {
                return next('not found member')
              }
              memberInf = result
              if(result.ship && result.ship.isAuthen) {
                isOrderActive = false
              }
              next();
            })
        }

        const findOrder = (next) => {
          if(!isOrderActive || region !== 'hn') {
            return next();
          }
          OrderSystem
            .count({
              shop: userId,
              status:{$in:[3,10]}
            })
            .limit(2)
            .lean()
            .exec((err,count) => {
              if(err) {
                return next(err)
              }
              if(count < 2) {
                isOrderActive = false
              }
              next();
            })
        }

        const getConfigShowHub = (next) => {
          if (region === 'vietnam:hatinh') {
            showHub = 1;
          }

          ConfigMerchant
            .findOne({
              status: 1,
              $or: [{
                member: userId
              }, {
                default: 1
              }]
            }, 'config')
            .sort('-member')
            .lean()
            .exec((err, result) => {
              if (err || !result || !result.config) {
                return next();
              }

              if (result.config.isOpenHub) {
                showHub = result.config.isOpenHub
              }

              next();
            })
        }

        const lisService = (next) => {
          Service.list(region, platform, nativeVersion, appName, (err, data) => {
            if(err) {
              return next(err);
            }

            const indexCar = _.findIndex(data, (item) => service.car.includes(item._id.toHexString()));

            data.forEach((item,i) => {
              // if(item._id.toHexString() === '6096369fa9b6fb64b4b7fddd' && userId !== '6063e806e55d457209ac0e6e') {
              //   if(mode !== 'shipper' && userId !== '6063e806e55d457209ac0e6e') {
              //     data.splice(i,1)
              //   }
              // }

              if (item.link === 'DeliveryHomeScreen' && !newDelivery) {
                item.link = 'OrderCreateScreen';
              }

              if(item.configTags && item.configTags[region]) {
                if(item.configTags[region].tagImage) {
                  item.tagImage = item.configTags[region].tagImage
                }
                if(item.configTags[region].tag) {
                  item.tag = item.configTags[region].tag
                }
              }

              if((item.regionDistrict && item.regionDistrict.length && req.user.regionDistrict && !item.regionDistrict.includes(req.user.regionDistrict)) || (appName !== 'driver' && !showHub && service.hub.includes(item._id.toHexString()))) {
                 data.splice(i,1)
              }
              // if(['5d4cea1268731c9493253a91', '5e8d575ded99aa22acde13e2', '60b9ffa2ceeaad1c947386a8'].includes(item._id.toHexString()) && !isOrderActive && region === 'hn') {
              //   item.active = 0;
              //   if(memberInf.ship && memberInf.ship.isAuthen) {
              //     item.message = "Dịch vụ đang bảo trì. Vui lòng quay lại sau ít phút nữa"
              //   }
              // }
              // if(memberInf && memberInf.shop.isAuthen && region === 'vietnam:danang'  && item.name !== 'Ưu đãi') {
              //   item.timeActive = {}
              //   item.timeActive['vietnam:danang'] = {
              //     "from" : 1,
              //     "to" : 2,
              //     "message" : "HeyU đang đợi xác nhận lại lần cuối của sở GTVT, sở TTTT và UBND TP Đà Nẵng về công văn mới nhất ban hành bổ sung tối ngày hôm qua (25/8) trước khi mở lại các dịch vụ trên ứng dụng. Kính báo."
              //   }
              // }
              let extras = item.extras || {}
              if(item.promote && item.promote[region]) {
                extras.promoteId = item.promote[region];
                item.extras = extras
              }

              if(item.timeActive && item.timeActive[region]) {
                const timeActive = item.timeActive[region];

                const today = new Date();
                const startDate = today.setHours(0,0,0,0)
                const currentHourInMs = Date.now() - startDate;

                if(timeActive.from && timeActive.to && (currentHourInMs < timeActive.from || currentHourInMs > timeActive.to)) {
                  item.active = 0;
                  item.message = timeActive.message
                }
              }
              if(blackArray.includes(item._id.toHexString())) {
                item.blockCreate = 1
              }

              if ((indexCar === -1 || !data[indexCar].active) && service.bike.includes(item._id.toHexString())) {
                item.nameTabShop = 'Chở khách';
              }

              if (mode === 'shipper') {
                if (!service.order.concat(service.bike).concat(service.hireDriver).includes(item._id.toHexString())) {
                  item.active = 0;
                }

                if (service.bike.includes(item._id.toHexString())) {
                  item.name = 'Di Chuyển';
                }

                if (service.heyCare.includes(item._id.toHexString()) || service.heyClean.includes(item._id.toHexString())) {
                  item.showTab = 0;
                }
              }

              if (mode === 'staff') {
                if (!service.heyCare.includes(item._id.toHexString()) && !service.heyClean.includes(item._id.toHexString())) {
                  item.active = 0;
                }
              }
            })

            next(null,{
              code: CONSTANTS.CODE.SUCCESS,
              data
            })
          })
        }

        async.waterfall([
          checkParams,
          countBikeOrder,
          // findUser,
          //findOrder,
          getConfigShowHub,
          lisService
        ], (err, data) => {
          if(_.isError(err)) {
            logger.logError([err], req.originalUrl, req.body);
          }
          err && _.isError(err) && (data = {
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });

          res.json(data || err);
        });
      },
    getBanner(req, res) {
      const region = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;
      const position = req.body.position || 1;
      const appName = req.body.appName || '';
      const versionCodePush = Number(_.get(req, 'body.versionCodePush', '').replace('v', ''));

      if(!region || !platform || !nativeVersion) {
        return res.json({
          code: CONSTANTS.CODE.WRONG_PARAMS
        });
      }

      const query = {
        position,
        appName: {
          '$exists': false
        },
        $or: [
          {
            'region.allow': region
          },
          {
            'region.allow': 'all',
            'region.deny': {
              $ne: region
            }
          }
        ]
      }

      if (appName && !['bookWeb', 'heyCareWeb'].includes(appName)) {
        query.appName = appName;
      }

      // Tạo function để tìm banner với fallback logic
      const findBannerWithFallback = () => {
        // Ưu tiên 1: Tìm theo versionCodePush trước (nếu có)
        if (versionCodePush && versionCodePush > 0) {
          const codePushQuery = {...query};
          codePushQuery[`platform.${platform}.codePushFrom`] = {$lte: Number(versionCodePush)};
          codePushQuery[`platform.${platform}.codePushTo`] = {$gte: Number(versionCodePush)};
          codePushQuery[`platform.${platform}.codePushDeny`] = {$ne: Number(versionCodePush)};

          let codePushFunc = Banner.findOne(codePushQuery, 'config').lean();

          if (environment === 'production') {
            codePushFunc = codePushFunc.cache(ms('15m')/1000, `banner:${region}:${platform}:codepush:${versionCodePush}:${position}`)
          }

          codePushFunc.exec((err, result) => {
            if (!err && result) {
              // Tìm được bằng versionCodePush
              return res.json({
                code: CONSTANTS.CODE.SUCCESS,
                data: result.config
              });
            }

            // Fallback: Tìm theo nativeVersion
            findByNativeVersion();
          });
        } else {
          // Không có versionCodePush, tìm theo nativeVersion luôn
          findByNativeVersion();
        }
      };

      const findByNativeVersion = () => {
        const nativeQuery = {...query};
        nativeQuery[`platform.${platform}.from`] = {$lte: Number(nativeVersion)};
        nativeQuery[`platform.${platform}.to`] = {$gte: Number(nativeVersion)};
        nativeQuery[`platform.${platform}.deny`] = {$ne: Number(nativeVersion)};

        let nativeFunc = Banner.findOne(nativeQuery, 'config').lean();

        if (environment === 'production') {
          nativeFunc = nativeFunc.cache(ms('15m')/1000, `banner:${region}:${platform}:native:${nativeVersion}:${position}`)
        }

        nativeFunc.exec((err, result) => {
          if (err || !result) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            });
          }

          return res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: result.config
          });
        });
      };

      // Bắt đầu tìm kiếm
      findBannerWithFallback()
    },
    getBannerFood(req, res) {
      const region = req.body.regionName;
      const platform = req.body.platform;
      const nativeVersion = req.body.nativeVersion;

      if(!region) {
        return res.json({
          code: CONSTANTS.CODE.WRONG_PARAMS
        });
      }
      const query = {
        $or: [
          {
            'region.allow': region
          },
          {
            'region.allow': 'all',
            'region.deny': {
              $ne: region
            }
          }
        ]
      }
      query[`platform.${platform}.from`] = {$lte: Number(nativeVersion)};
      query[`platform.${platform}.to`] = {$gte: Number(nativeVersion)};
      query[`platform.${platform}.deny`] = {$ne: Number(nativeVersion)};

      let func = BannerFood.findOne(query,'config')
      .lean()

      if (environment === 'production') {
        func = func.cache(ms('15m')/1000, `bannerfood:${region}:${platform}:${nativeVersion}`)
      }


      func.exec((err, result) => {
        if (err) {
          return res.json({
            code: CONSTANTS.CODE.SYSTEM_ERROR
          });
        }
        return res.json({
          code: CONSTANTS.CODE.SUCCESS,
          data: result && result.config || []
        });
      })
    },
    getConfigForLending(req,res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.LENDING, req.body.regionName, (err, data) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
          let result = {
            isOpen: 0,
            serviceName: 'Tạm ứng thanh toán'
          }

          if(data.config) {
            result.isOpen = data.config.isOpen
            result.method = data.config.method
          }
          if(req.body.platform === 'ios') {
            result.isOpen = 0
          }
          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: result
          })
        })
    },
    listServiceRegister(req, res) {
      const {regionName} = req.body;
      const id = req.user.id;

      let services;
      let isRiderAuthen = 0

      const checkParams = (next) => {
        if(!regionName || !id) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS
          })
        }
        next();
      }

      const findRiderInf = (next) => {
        Members
          .findById(id)
          .select('ship.isAuthen')
          .lean()
          .exec((err, result) => {
            if(err) {
              return next(err)
            }
            if(result && result.ship && result.ship.isAuthen){
              isRiderAuthen = result.ship.isAuthen
            }
            next();
          })
      }

      const listServiceAvailable = (next) => {
        OrderType
          .find({
            status: 1,
            showListShipper: 1,
            $or: [
              {
                'region.allow': regionName
              },
              {
                'region.allow': 'all',
                'region.deny': {
                  $ne: regionName
                }
              }
            ]
          })
          .select({
            name: 1,
            icon: 1,
            editable: 1,
            messageForRider: 1,
            canRegister: 1,
            serviceRaw: 1
          })
          .sort('-order')
          .lean()
          .exec((err, results) => {
            if(err) {
              return next(err)
            }

            services = results;
            services.map(item => {
              if (service.order.concat(service.lend).concat(service.food).concat(service.errand).includes(item.serviceRaw.toHexString())) {
                item.service = service.order;
              }

              if (service.bike.concat(service.car).includes(item.serviceRaw.toHexString())) {
                item.service = service.bike;
              }

              if (service.hireDriver.includes(item.serviceRaw.toHexString())) {
                item.service = service.hireDriver;
              }
            })

            next();
          })
      }

      const findRunningService = (next) => {
        Rider
          .findOne({
            member: id
          })
          .lean()
          .select({
            serviceRunning: 1,
            serviceRegisted: 1
          })
          .exec((err, result) => {
            if(err) {
              return next(err);
            }

            if(!result || !result.serviceRunning) {
              return next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                data:{
                  services,
                  count:0
                }
              })
            }

            let count = 0;
            const serviceRunning = result.serviceRunning.map(String)
            services.some((service) => {
              if(serviceRunning.includes(service._id.toString())) {
                service.active = 1;
                count ++;
              }
              if(!orderTypeGroup.bike.includes(service._id.toString()) && isRiderAuthen) {
                service.editable = 0;
              }

              if(orderTypeGroup.car4.concat(orderTypeGroup.car7).includes(service._id.toString()) && isRiderAuthen) {
                service.canRegister = 0;
              }
            })

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                services,
                count
              }
            })
          })
      }

      async.waterfall([
        checkParams,
        findRiderInf,
        listServiceAvailable,
        findRunningService
      ], (err, data) => {
        if(_.isError(err)) {
          logger.logError([err], req.originalUrl, req.body);
        }
        err && _.isError(err) && (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR
        });

        res.json(data || err);
      });
    },
    getContact(req, res) {
      const deviceInf = _.get(req,'body.device', '')
      Config
        .getData({
          type: CONSTANTS.CONFIG_TYPE.CONTACT,
          region: req.body.regionName,
          regionDistrict: req.user.regionDistrict || ''
        }, (err, data) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
          if(data && data.config && deviceInf) {
            data.config.canCallNormal = true
            if(deviceInf && data.config.blackListDevices) {
              data.config.blackListDevices.forEach((blackDevice) => {
                if(deviceInf.deviceBrand.includes(blackDevice)) {
                  data.config.canCallNormal = false;
                }
              })
            }
          }
          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: data ? data.config : null
          })
        })
    },
    getConfigShowNews(req,res) {
      Config
        .get(CONSTANTS.CONFIG_TYPE.SHOW_NEWS, req.body.regionName, (err, data) => {
          if(err) {
            return res.json({
              code: CONSTANTS.CODE.SYSTEM_ERROR
            })
          }
          let config = []
          if(data && data.config) {
            config = data.config
          }
          res.json({
            code: CONSTANTS.CODE.SUCCESS,
            data: config
          })
        })
    }
}
