var FeedBackSchema = new mongoose.Schema({
  member : {type: mongoose.Schema.Types.ObjectId, ref: 'Member'},
  image: {
    type: mongoose.Schema.Types.Mixed
  },
  message: {
    type: 'String'
  },
  type: {
    type: Number
  },
  note: {
    type: String
  },
  orders: {
    type:  [mongoose.Schema.Types.ObjectId]
  },
  jobId: {
    type: mongoose.Schema.Types.ObjectId
  },
  orderStore: {
    type:  [mongoose.Schema.Types.ObjectId]
  },
  mode: {
    type: String
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  region: {
    type: String
  },
  status: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
  }
}, {id: false, versionKey: false});

// Add indexes for better query performance
FeedBackSchema.index({ member: 1, mode: 1, createdAt: -1 });
FeedBackSchema.index({ createdAt: -1 });

// module.exports = mongoose.model('FeedBack', FeedBackSchema);
module.exports = mongoConnections('master').model('FeedBack', FeedBackSchema);
