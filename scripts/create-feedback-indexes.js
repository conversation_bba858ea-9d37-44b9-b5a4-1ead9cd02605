#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create database indexes for Feedback collection
 * This will improve query performance for getFeedbackList API
 */

const mongoose = require('mongoose');
const config = require('../src/config');

// Connect to MongoDB
const mongoUri = `mongodb://${config.mongo.connections.master.host}:${config.mongo.connections.master.port}/${config.mongo.connections.master.database}`;

async function createIndexes() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri, config.mongo.connections.master.options);
    
    const db = mongoose.connection.db;
    const feedbackCollection = db.collection('feedbacks');
    
    console.log('Creating indexes for Feedback collection...');
    
    // Compound index for the main query: member + mode + createdAt
    await feedbackCollection.createIndex(
      { member: 1, mode: 1, createdAt: -1 },
      { 
        name: 'feedback_member_mode_createdAt',
        background: true 
      }
    );
    console.log('✓ Created compound index: member + mode + createdAt');
    
    // Index for createdAt (for general sorting)
    await feedbackCollection.createIndex(
      { createdAt: -1 },
      { 
        name: 'feedback_createdAt',
        background: true 
      }
    );
    console.log('✓ Created index: createdAt');
    
    // Index for member (for user-specific queries)
    await feedbackCollection.createIndex(
      { member: 1 },
      { 
        name: 'feedback_member',
        background: true 
      }
    );
    console.log('✓ Created index: member');
    
    // List all indexes to verify
    const indexes = await feedbackCollection.indexes();
    console.log('\nCurrent indexes on Feedback collection:');
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });
    
    console.log('\n✅ All indexes created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating indexes:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createIndexes();
